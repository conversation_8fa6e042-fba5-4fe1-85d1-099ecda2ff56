"""
Stage 10 V2: Script Playground - Clean Template-Based Generation Tool
GretahAI ScriptWeaver

PHASE 1: Core Foundation & Basic Template Loading

This is a complete rebuild of Stage 10 using a phased approach to deliver
a clean, streamlined template-based script generation tool that eliminates
UI complexity and Streamlit rerun issues.

Phase 1 Features:
- Minimal viable Stage 10 with basic StateManager integration
- Template discovery and loading from Stages 1-8 output directories
- Simple template selection UI with two-column preview layout
- Basic stage transition guards and navigation
- Immediate testing capability through app.py

Key Design Principles:
- Pure template-based generation (no interactive element selectors)
- Clean StateManager integration following established patterns
- Modular organization with clear separation of concerns
- Defensive programming with explicit error handling
- Progressive disclosure UI with minimal rerun triggers
- Enterprise styling with professional appearance

© 2025 Cogniron All Rights Reserved.
"""

import streamlit as st
import logging
import os
import json
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass

# Core imports
from state_manager import StateManager, StateStage
from core.config import APP_CONFIG_FILE

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage10_v2")

# Debug mode from environment
DEBUG_MODE = os.environ.get("SCRIPTWEAVER_DEBUG", "false").lower() == "true"

def _debug_log(message: str, category: str = "general") -> None:
    """
    Centralized debug logging for Stage 10 V2.
    
    Args:
        message: Debug message to log
        category: Category for organizing debug output
    """
    if DEBUG_MODE:
        timestamp = datetime.now().strftime("%H:%M:%S.%f")[:-3]
        logger.debug(f"[{timestamp}] [stage10_v2.{category}] {message}")

@dataclass
class TemplateInfo:
    """Data class for template information."""
    id: str
    name: str
    objective: str
    creation_time: str
    optimization_status: str
    file_path: str
    metadata: Dict[str, Any]

class Stage10StateManager:
    """
    Centralized state management for Stage 10 V2.
    
    Handles all Stage 10 specific state operations while integrating
    with the main StateManager for consistency.
    """
    
    def __init__(self, state: StateManager):
        self.state = state
        self._ensure_stage10_state()
    
    def _ensure_stage10_state(self) -> None:
        """Ensure Stage 10 specific state attributes exist."""
        # API key management
        if not hasattr(self.state, 'stage10_google_api_key'):
            self.state.stage10_google_api_key = None
        
        # Template selection
        if not hasattr(self.state, 'stage10_selected_template'):
            self.state.stage10_selected_template = None
        
        # Test case selection
        if not hasattr(self.state, 'stage10_selected_test_case'):
            self.state.stage10_selected_test_case = None
        
        # UI state
        if not hasattr(self.state, 'stage10_show_template_preview'):
            self.state.stage10_show_template_preview = False
        
        _debug_log("Stage 10 state attributes ensured", "state")
    
    def load_api_key(self) -> Optional[str]:
        """
        Load Google AI API key from config.json or environment.
        
        Returns:
            API key string or None if not found
        """
        try:
            # Check if already loaded in state
            if self.state.stage10_google_api_key:
                _debug_log("API key found in state", "api")
                return self.state.stage10_google_api_key
            
            # Try config file first
            if os.path.exists(APP_CONFIG_FILE):
                with open(APP_CONFIG_FILE, "r") as f:
                    config = json.load(f)
                    if "google_api_key" in config and config["google_api_key"]:
                        self.state.stage10_google_api_key = config["google_api_key"]
                        _debug_log("API key loaded from config file", "api")
                        return self.state.stage10_google_api_key
            
            # Fall back to environment variable
            env_key = os.environ.get("GOOGLE_API_KEY", "")
            if env_key:
                self.state.stage10_google_api_key = env_key
                _debug_log("API key loaded from environment", "api")
                return env_key
            
            _debug_log("No API key found in config or environment", "api")
            return None
            
        except Exception as e:
            _debug_log(f"Error loading API key: {e}", "api")
            return None
    
    def set_selected_template(self, template: Optional[TemplateInfo]) -> None:
        """Set the selected template with state update."""
        self.state.stage10_selected_template = template
        _debug_log(f"Template selected: {template.name if template else 'None'}", "template")
    
    def get_selected_template(self) -> Optional[TemplateInfo]:
        """Get the currently selected template."""
        return getattr(self.state, 'stage10_selected_template', None)

def load_available_templates() -> List[TemplateInfo]:
    """
    Load available templates from Stages 1-8 output directories.
    
    Returns:
        List of TemplateInfo objects representing available templates
    """
    templates = []
    _debug_log("Starting template discovery", "template")
    
    try:
        # This is a placeholder for Phase 1
        # In later phases, this will scan actual output directories
        # For now, return mock data for testing
        mock_templates = [
            TemplateInfo(
                id="template_001",
                name="Login Test Template",
                objective="Verify user login functionality with valid credentials",
                creation_time="2025-01-27 10:30:00",
                optimization_status="Optimized",
                file_path="/mock/path/login_test.py",
                metadata={"test_case_id": "TC001", "stage": "Stage 8"}
            ),
            TemplateInfo(
                id="template_002", 
                name="Search Functionality Template",
                objective="Test search feature with various input types",
                creation_time="2025-01-27 11:15:00",
                optimization_status="Generated",
                file_path="/mock/path/search_test.py",
                metadata={"test_case_id": "TC002", "stage": "Stage 6"}
            )
        ]
        
        templates.extend(mock_templates)
        _debug_log(f"Loaded {len(templates)} templates", "template")
        
    except Exception as e:
        _debug_log(f"Error loading templates: {e}", "template")
        st.error(f"❌ **Template Loading Error**: {e}")
    
    return templates

def render_template_selection(stage10_state: Stage10StateManager, templates: List[TemplateInfo]) -> None:
    """
    Render the template selection interface.
    
    Args:
        stage10_state: Stage 10 state manager
        templates: List of available templates
    """
    _debug_log("Rendering template selection", "ui")
    
    if not templates:
        st.info("🎮 **No Templates Available**")
        st.markdown("Create optimized scripts through Stages 1-8 to use as templates.")
        return
    
    st.markdown("### 📋 Template Selection")
    
    # Create template options for selectbox
    template_options = ["Select a template..."] + [
        f"{template.name} ({template.creation_time})" 
        for template in templates
    ]
    
    # Template selection
    selected_option = st.selectbox(
        "Choose a template to use for script generation:",
        template_options,
        key="stage10_v2_template_selection"
    )
    
    # Handle template selection
    if selected_option != "Select a template...":
        # Find the selected template
        selected_index = template_options.index(selected_option) - 1  # -1 for placeholder
        selected_template = templates[selected_index]
        
        # Update state
        stage10_state.set_selected_template(selected_template)
        
        # Show template preview
        render_template_preview(selected_template)
        
        _debug_log(f"Template selected: {selected_template.name}", "ui")
    else:
        # Clear selection
        stage10_state.set_selected_template(None)

def render_template_preview(template: TemplateInfo) -> None:
    """
    Render template preview in two-column layout.
    
    Args:
        template: Template to preview
    """
    _debug_log(f"Rendering preview for template: {template.name}", "ui")
    
    st.markdown("### 👁️ Template Preview")
    
    # Two-column layout as specified
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("**Template Objective:**")
        st.info(template.objective)
    
    with col2:
        st.markdown("**Template Information:**")
        info_text = f"""
        **ID:** {template.id}
        **Status:** {template.optimization_status}
        **Created:** {template.creation_time}
        **Source:** {template.metadata.get('stage', 'Unknown')}
        """
        st.markdown(info_text)

def validate_stage10_prerequisites(stage10_state: Stage10StateManager) -> Tuple[bool, str]:
    """
    Validate prerequisites for Stage 10 operation.
    
    Args:
        stage10_state: Stage 10 state manager
        
    Returns:
        Tuple of (is_valid, error_message)
    """
    _debug_log("Validating Stage 10 prerequisites", "validation")
    
    # Check API key availability
    api_key = stage10_state.load_api_key()
    if not api_key:
        return False, "Google AI API key is required for script generation"
    
    _debug_log("Prerequisites validation passed", "validation")
    return True, ""

def stage10_v2_script_playground(state: StateManager) -> None:
    """
    Stage 10 V2: Script Playground - Phase 1 Implementation
    
    This is the main entry point for Stage 10 V2, providing a clean,
    streamlined template-based script generation tool.
    
    Args:
        state: StateManager instance
    """
    _debug_log("=== Stage 10 V2 Script Playground Started ===", "main")
    
    # Initialize Stage 10 state manager
    stage10_state = Stage10StateManager(state)
    
    # Page header
    st.markdown("# 🎮 Script Playground V2")
    st.markdown("*Template-based script generation using pre-validated automation patterns*")
    
    # Validate prerequisites
    is_valid, error_message = validate_stage10_prerequisites(stage10_state)
    if not is_valid:
        st.error(f"❌ **Setup Required**: {error_message}")
        st.info("💡 **Note**: API key will be automatically loaded from config.json or environment variables.")
        return
    
    # Load available templates
    templates = load_available_templates()
    
    # Main template selection interface
    with st.expander("🚀 Template Selection", expanded=True):
        render_template_selection(stage10_state, templates)
    
    # Show current selection status
    selected_template = stage10_state.get_selected_template()
    if selected_template:
        st.success(f"✅ **Template Selected**: {selected_template.name}")
        
        # Phase 1 completion indicator
        st.info("🎯 **Phase 1 Complete**: Template selection and preview functional. Next: Test case integration.")
    else:
        st.info("👆 **Select a template above to continue**")
    
    _debug_log("=== Stage 10 V2 Script Playground Completed ===", "main")
