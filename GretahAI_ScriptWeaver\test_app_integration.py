#!/usr/bin/env python3
"""
Test script to verify Stage 10 V2 integration with the main app.py.

This script tests the app routing and integration without running the full Streamlit server.
It verifies that Stage 10 V2 can be properly accessed through the main application.
"""

import sys
import os
from unittest.mock import Mock, patch

# Add the current directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_app_imports():
    """Test that the main app can import Stage 10 V2."""
    print("🧪 Testing app.py imports...")
    
    try:
        # Test the import that app.py uses
        from stages import stage10_v2_script_playground
        print("✅ App import of Stage 10 V2 successful")
        return True
    except ImportError as e:
        print(f"❌ App import error: {e}")
        return False

def test_state_stage_enum():
    """Test that StateStage enum includes Stage 10 V2."""
    print("🧪 Testing StateStage enum...")
    
    try:
        from state_manager import StateStage
        
        # Verify the new stage exists
        assert hasattr(StateStage, 'STAGE10_V2_PLAYGROUND')
        
        # Test stage properties
        stage = StateStage.STAGE10_V2_PLAYGROUND
        stage_number = stage.get_stage_number()
        display_name = stage.get_display_name()
        
        print(f"✅ Stage 10 V2 enum: Number={stage_number}, Name='{display_name}'")
        return True
    except Exception as e:
        print(f"❌ StateStage enum error: {e}")
        return False

def test_app_routing_logic():
    """Test that app.py routing logic can handle Stage 10 V2."""
    print("🧪 Testing app routing logic...")
    
    try:
        from state_manager import StateManager, StateStage
        from stages import stage10_v2_script_playground
        
        # Create mock Streamlit
        mock_st = Mock()
        mock_st.session_state = {}
        
        # Initialize state
        state_manager = StateManager()
        state_manager.init_in_session(mock_st)
        state = StateManager.get(mock_st)
        
        # Set current stage to Stage 10 V2
        state.current_stage = StateStage.STAGE10_V2_PLAYGROUND
        
        # Mock Streamlit functions that Stage 10 V2 uses
        with patch('streamlit.markdown') as mock_markdown, \
             patch('streamlit.expander') as mock_expander, \
             patch('streamlit.selectbox') as mock_selectbox, \
             patch('streamlit.info') as mock_info, \
             patch('streamlit.error') as mock_error, \
             patch('streamlit.success') as mock_success:
            
            # Configure mocks
            mock_expander.return_value.__enter__ = Mock()
            mock_expander.return_value.__exit__ = Mock(return_value=None)
            mock_selectbox.return_value = "Select a template..."
            
            # Test that the function can be called without errors
            stage10_v2_script_playground(state)
            
            # Verify that UI functions were called
            assert mock_markdown.called
            assert mock_expander.called
            
        print("✅ App routing logic working correctly")
        return True
    except Exception as e:
        print(f"❌ App routing logic error: {e}")
        return False

def test_navigation_integration():
    """Test that Stage 10 V2 is integrated into navigation."""
    print("🧪 Testing navigation integration...")
    
    try:
        from core.stage_navigation import render_stage_navigation
        from state_manager import StateManager, StateStage
        
        # Create mock state
        mock_st = Mock()
        mock_st.session_state = {}
        state_manager = StateManager()
        state_manager.init_in_session(mock_st)
        state = StateManager.get(mock_st)
        
        # Mock Streamlit navigation functions
        with patch('streamlit.markdown') as mock_markdown, \
             patch('streamlit.button') as mock_button:
            
            mock_button.return_value = False
            
            # Test navigation rendering (should not crash)
            result = render_stage_navigation(state)
            
            # Should return None (no navigation triggered)
            assert result is None
            
        print("✅ Navigation integration working correctly")
        return True
    except Exception as e:
        print(f"❌ Navigation integration error: {e}")
        return False

def test_stage_transition():
    """Test stage transition to Stage 10 V2."""
    print("🧪 Testing stage transition...")
    
    try:
        from state_manager import StateManager, StateStage
        
        # Create mock state
        mock_st = Mock()
        mock_st.session_state = {}
        state_manager = StateManager()
        state_manager.init_in_session(mock_st)
        state = StateManager.get(mock_st)
        
        # Test transition to Stage 10 V2
        success = state.advance_to(StateStage.STAGE10_V2_PLAYGROUND, "Testing Phase 1")
        
        assert success == True
        assert state.current_stage == StateStage.STAGE10_V2_PLAYGROUND
        
        print("✅ Stage transition working correctly")
        return True
    except Exception as e:
        print(f"❌ Stage transition error: {e}")
        return False

def run_integration_tests():
    """Run all integration tests."""
    print("🚀 Running Stage 10 V2 App Integration Tests")
    print("=" * 60)
    
    tests = [
        test_app_imports,
        test_state_stage_enum,
        test_app_routing_logic,
        test_navigation_integration,
        test_stage_transition
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    print("=" * 60)
    print(f"📊 Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All integration tests passed!")
        print("✅ Stage 10 V2 Phase 1 is fully integrated and ready for use")
        print("🎯 Next: Access Stage 10 V2 through the main application interface")
        print("   → Navigate to 'Script Playground V2' in the sidebar")
    else:
        print("⚠️  Some integration tests failed. Please fix issues before proceeding.")
    
    return passed == total

if __name__ == "__main__":
    success = run_integration_tests()
    sys.exit(0 if success else 1)
