#!/usr/bin/env python3
"""
Test script for Stage 10 V2 Phase 1 implementation.

This script verifies that the Phase 1 implementation is working correctly:
1. Stage 10 V2 can be imported and initialized
2. StateManager integration works properly
3. Template loading functionality works
4. UI components render without errors
5. Navigation integration is functional

Run this test to verify Phase 1 completion before moving to Phase 2.
"""

import sys
import os
import tempfile
import json
from unittest.mock import Mock, patch

# Add the current directory to the path
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_stage10_v2_imports():
    """Test that Stage 10 V2 can be imported successfully."""
    print("🧪 Testing Stage 10 V2 imports...")
    
    try:
        from stages.stage10_v2 import (
            stage10_v2_script_playground,
            Stage10StateManager,
            TemplateInfo,
            load_available_templates,
            validate_stage10_prerequisites
        )
        print("✅ All Stage 10 V2 imports successful")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_state_manager_integration():
    """Test StateManager integration with Stage 10 V2."""
    print("🧪 Testing StateManager integration...")
    
    try:
        from state_manager import StateManager, StateStage
        from stages.stage10_v2 import Stage10StateManager
        
        # Create mock Streamlit session state
        mock_st = Mock()
        mock_st.session_state = {}
        
        # Initialize StateManager
        state_manager = StateManager()
        state_manager.init_in_session(mock_st)
        state = StateManager.get(mock_st)
        
        # Test Stage 10 V2 state manager
        stage10_state = Stage10StateManager(state)
        
        # Verify state attributes are created
        assert hasattr(state, 'stage10_google_api_key')
        assert hasattr(state, 'stage10_selected_template')
        assert hasattr(state, 'stage10_selected_test_case')
        
        print("✅ StateManager integration working correctly")
        return True
    except Exception as e:
        print(f"❌ StateManager integration error: {e}")
        return False

def test_template_info_dataclass():
    """Test TemplateInfo dataclass functionality."""
    print("🧪 Testing TemplateInfo dataclass...")
    
    try:
        from stages.stage10_v2 import TemplateInfo
        
        # Create test template
        template = TemplateInfo(
            id="test_001",
            name="Test Template",
            objective="Test objective",
            creation_time="2025-01-27 10:00:00",
            optimization_status="Optimized",
            file_path="/test/path.py",
            metadata={"test": "data"}
        )
        
        # Verify attributes
        assert template.id == "test_001"
        assert template.name == "Test Template"
        assert template.metadata["test"] == "data"
        
        print("✅ TemplateInfo dataclass working correctly")
        return True
    except Exception as e:
        print(f"❌ TemplateInfo error: {e}")
        return False

def test_template_loading():
    """Test template loading functionality."""
    print("🧪 Testing template loading...")
    
    try:
        from stages.stage10_v2 import load_available_templates
        
        # Load templates (should return mock data in Phase 1)
        templates = load_available_templates()
        
        # Verify templates are loaded
        assert len(templates) > 0
        assert all(hasattr(t, 'id') for t in templates)
        assert all(hasattr(t, 'name') for t in templates)
        assert all(hasattr(t, 'objective') for t in templates)
        
        print(f"✅ Template loading working correctly ({len(templates)} templates loaded)")
        return True
    except Exception as e:
        print(f"❌ Template loading error: {e}")
        return False

def test_api_key_loading():
    """Test API key loading functionality."""
    print("🧪 Testing API key loading...")
    
    try:
        from state_manager import StateManager
        from stages.stage10_v2 import Stage10StateManager
        
        # Create temporary config file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.json', delete=False) as f:
            config = {"google_api_key": "test_api_key_12345"}
            json.dump(config, f)
            temp_config_path = f.name
        
        # Mock the config file path
        with patch('stages.stage10_v2.APP_CONFIG_FILE', temp_config_path):
            # Create mock state
            mock_st = Mock()
            mock_st.session_state = {}
            state_manager = StateManager()
            state_manager.init_in_session(mock_st)
            state = StateManager.get(mock_st)
            
            # Test API key loading
            stage10_state = Stage10StateManager(state)
            api_key = stage10_state.load_api_key()
            
            assert api_key == "test_api_key_12345"
        
        # Clean up
        os.unlink(temp_config_path)
        
        print("✅ API key loading working correctly")
        return True
    except Exception as e:
        print(f"❌ API key loading error: {e}")
        return False

def test_stage_enum_integration():
    """Test StateStage enum integration."""
    print("🧪 Testing StateStage enum integration...")
    
    try:
        from state_manager import StateStage
        
        # Verify new stage exists
        assert hasattr(StateStage, 'STAGE10_V2_PLAYGROUND')
        
        # Test stage number
        stage = StateStage.STAGE10_V2_PLAYGROUND
        assert stage.get_stage_number() == 11
        
        # Test display name
        display_name = stage.get_display_name()
        assert "V2" in display_name
        assert "Script Playground" in display_name
        
        print("✅ StateStage enum integration working correctly")
        return True
    except Exception as e:
        print(f"❌ StateStage enum integration error: {e}")
        return False

def run_all_tests():
    """Run all Phase 1 tests."""
    print("🚀 Running Stage 10 V2 Phase 1 Tests")
    print("=" * 50)
    
    tests = [
        test_stage10_v2_imports,
        test_state_manager_integration,
        test_template_info_dataclass,
        test_template_loading,
        test_api_key_loading,
        test_stage_enum_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All Phase 1 tests passed! Stage 10 V2 Phase 1 is ready for integration testing.")
        print("🎯 Next step: Test through main app.py interface")
    else:
        print("⚠️  Some tests failed. Please fix issues before proceeding.")
    
    return passed == total

if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
